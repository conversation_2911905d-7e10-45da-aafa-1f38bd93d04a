{"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.3.2", "maintainers": ["<PERSON> <<EMAIL>> (github.com/qix-)", "Sindre Sorhus <<EMAIL>> (sindresorhus.com)"], "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "xo": {"rules": {"operator-linebreak": [0]}}, "repository": "qix-/node-error-ex", "files": ["index.js"], "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "dependencies": {"is-arrayish": "^0.2.1"}}