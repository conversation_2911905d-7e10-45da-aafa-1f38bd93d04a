{"name": "electron-store", "version": "10.0.1", "description": "Simple data persistence for your Electron app or module - Save and load user settings, app state, cache, etc", "license": "MIT", "repository": "sindresorhus/electron-store", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=20"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["electron", "store", "app", "config", "storage", "conf", "configuration", "settings", "preferences", "json", "data", "persist", "persistent", "save"], "dependencies": {"conf": "^13.0.0", "type-fest": "^4.20.0"}, "devDependencies": {"ava": "^6.1.3", "electron": "^31.0.1", "execa": "^9.2.0", "tsd": "^0.31.0", "xo": "^0.58.0"}, "xo": {"envs": ["node", "browser"]}, "tsd": {"compilerOptions": {"module": "node16", "moduleResolution": "node16", "moduleDetection": "force"}}}